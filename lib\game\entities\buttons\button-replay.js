ig.module('game.entities.buttons.button-replay')
.requires(
    'plugins.utils.buttons.button-base'
)
.defines(function () {
    "use strict";

    ig.EntityButtonReplay = ig.global.EntityButtonReplay = EntityButtonBase.extend({
        name: 'button-replay',
        idleSheetInfo: { sheetImage: new ig.Image('media/graphics/sprites/ui/btn-replay.png'), frameCountX: 1, frameCountY: 1 },
        buttonTextConfig: {
            fontSize: 48,
            fontFamily: 'bebasneue-bold',
            fontColor: '#000000',
            text: _STRINGS['Game']['Buttons']['Replay'],
            align: 'center',
            vAlign: 'middle'
        },
        hasText: true,
        buttonTextOffset: { x: 20, y: 5 },
        init: function (x, y, settings) {
            this.parent(x, y, settings);

            this.buttonTextConfig.width = this.size.x * 0.9;
            this.buttonTextConfig.height = this.size.y;

            this.idle = new ig.Animation(this.idleSheet, 1, [0], true);
            this.currentAnim = this.idle;

            ig.game.sortEntitiesDeferred();
        },

        onClickCallback: function () {
            ig.currentCtrl.transition.fadeIn(500, function () {
                ig.game.director.reloadLevel();
            }.bind(this));
        },
        
        update: function () {
            this.parent();
        },
        
        draw: function () {
            this.parent();
        }
    });
});
