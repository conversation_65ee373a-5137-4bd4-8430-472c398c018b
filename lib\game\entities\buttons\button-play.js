ig.module('game.entities.buttons.button-play')
.requires(
    'plugins.utils.buttons.button-base',
    'game.entities.objects.popup-level-select'
)
.defines(function () {
    "use strict";

    ig.EntityButtonPlay = ig.global.EntityButtonPlay = EntityButtonBase.extend({
        name: 'button-play',
        idleSheetInfo: { sheetImage: new ig.Image('media/graphics/sprites/ui/btn-play.png'), frameCountX: 1, frameCountY: 1 },
        buttonTextConfig: {
            fontSize: 72,
            fontFamily: 'bebasneue-bold',
            fontColor: '#fff',
            text: _STRINGS['Game']['Buttons']['Play'],
            align: 'left',
            vAlign: 'bottom'
        },
        hasText: true,
        buttonTextOffset: { x: 0, y: 0 },
        init: function (x, y, settings) {
            this.parent(x, y, settings);

            this.idle = new ig.Animation(this.idleSheet, 1, [0], true);
            this.currentAnim = this.idle;

            this.buttonTextOffset.x = this.size.x * 0.25;

            this.textEntity.anchorTo(
                this, /* Target entity: this button */
                {
                    targetAnchor: { x: 0, y: 0 }, /* Target anchor: center of the button */
                    offset: this.buttonTextOffset /* Pixel offset */
                }
            );

            ig.game.sortEntitiesDeferred();
        },

        onClickCallback: function () {
            // spawn level select
            this._parent.tweenHide();
            ig.game.spawnEntity(EntityPopupLevelSelect, 0, 0, { zIndex: ig.game.LAYERS.POPUP });
        },
        
        update: function () {
            this.parent();
        },
        
        draw: function () {
            this.parent();
        }
    });
});
