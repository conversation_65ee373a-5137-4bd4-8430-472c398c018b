ig.module('game.entities.objects.popup-level-select')
.requires(
    'plugins.utils.objects.popup-base',
    'game.entities.text',
    'game.entities.buttons.button-nav',
    'game.data.level-data'
)
.defines(function () {

    EntityPopupLevelSelect = EntityPopupBase.extend({
        name: 'popup-level-select',

        // --- Configuration for the Level Select Popup ---

        // Override default header text settings from EntityPopupBase
        headerTextConfig: {
            text: 'Level 1',
            fontSize: 120,
            fontFamily: 'bebasneue-bold',
            fontColor: '#FFFFFF',
            align: 'center',
            vAlign: 'middle',
            shadowEnabled: true,
            shadowOffsetX: 4,
            shadowOffsetY: 4,
            shadowBlur: 1,
            shadowColor: '#000000',
            overflow: true
        },

        // Offset for the body text relative to the popup's center
        // Adjust these values to position the body text appropriately below the header
        headerTextOffset: { x: 0, y: 60 },

        displayOverlay: false,

        hasCloseButton: true,

        thumbnailList: [
            new ig.Image('media/graphics/sprites/maps/map-1-preview.png'),
            new ig.Image('media/graphics/sprites/maps/map-2-preview.png'),
            new ig.Image('media/graphics/sprites/maps/map-3-preview.png')
        ],

        thumbnailImage: null,

        currentThumbnailIndex: 0,

        init: function (x, y, settings) {
            this.parent(x, y, settings);

            this.elements.buttons = {};
            this.elements.buttons.next = this.spawnEntity(
                EntityButtonNext,
                this.pos.x,
                this.pos.y
            );
            this.elements.buttons.prev = this.spawnEntity(
                EntityButtonPrev,
                this.pos.x,
                this.pos.y
            );

            this.levelDataManager = new LevelData();

            this.thumbnailImage = this.thumbnailList[this.currentThumbnailIndex];
            this.elements.headerText.setTextContent(this.levelDataManager.getLevelData("level1").title);

            this.ctx = ig.system.context;

            // Update button states based on initial index
            this.updateButtonStates();

            ig.game.sortEntitiesDeferred();
        },
        
        onNext: function () {
            // Only proceed if not at the last item
            if (this.currentThumbnailIndex < this.thumbnailList.length - 1) {
                this.currentThumbnailIndex++;
                this.thumbnailImage = this.thumbnailList[this.currentThumbnailIndex];
                this.elements.headerText.setTextContent(this.levelDataManager.getLevelData("level" + (this.currentThumbnailIndex + 1)).title);
                this.updateButtonStates();
            }
        },

        onPrev: function () {
            // Only proceed if not at the first item
            if (this.currentThumbnailIndex > 0) {
                this.currentThumbnailIndex--;
                this.thumbnailImage = this.thumbnailList[this.currentThumbnailIndex];
                this.elements.headerText.setTextContent(this.levelDataManager.getLevelData("level" + (this.currentThumbnailIndex + 1)).title);
                this.updateButtonStates();
            }
        },

        updateButtonStates: function () {
            if (this.elements.buttons) {
                // Disable prev button if at first item
                if (this.currentThumbnailIndex <= 0) {
                    this.elements.buttons.prev.hide();
                } else {
                    this.elements.buttons.prev.show();
                }

                // Disable next button if at last item
                if (this.currentThumbnailIndex >= this.thumbnailList.length - 1) {
                    this.elements.buttons.next.hide();
                } else {
                    this.elements.buttons.next.show();
                }
            }
        },

        updateElementsAlpha: function (alpha) {
            this.parent(alpha); // Call parent method to update header, close button, overlay

            if (this.elements.buttons) {
                for (var key in this.elements.buttons) {
                    if (this.elements.buttons[key] && typeof this.elements.buttons[key].updateAlpha === 'function') {
                        this.elements.buttons[key].updateAlpha(alpha);
                    }
                }
            }
        },

        updateElementsPosition: function () {
            this.parent(); // Call parent method to update header, close button

            if (this.elements.buttons) {
                // position next and prev buttons at the sides of the popup
                var padding = 20;
                this.elements.buttons.next.pos.x = this.pos.x + this.size.x - this.elements.buttons.next.size.x - padding;
                this.elements.buttons.next.pos.y = this.pos.y + this.size.y * 0.5;

                this.elements.buttons.prev.pos.x = this.pos.x + padding;
                this.elements.buttons.prev.pos.y = this.elements.buttons.next.pos.y;
            }

            if (this.elements.headerText) this.elements.headerText._updateAnchorPosition();
        },

        exitCb: function () {
            ig.currentCtrl.tweenShow();
        },

        kill: function () {
            if (this.elements.buttons) {
                this.elements.buttons.next.kill();
                this.elements.buttons.prev.kill();      
            }
            // Kill other custom buttons if added
            this.parent();
        },

        update: function () {
            this.parent();
        },

        draw: function () {
            this.parent();
            this.ctx.save();
            this.ctx.globalAlpha = this.popupAlpha;
            if (this.thumbnailImage) {
                this.thumbnailImage.draw(this.pos.x + this.size.x * 0.5 - this.thumbnailImage.width * 0.5, this.pos.y + this.size.y * 0.5 + this.headerTextOffset.y - this.thumbnailImage.height * 0.5);
            }
            this.ctx.restore();
        }
    });
});
