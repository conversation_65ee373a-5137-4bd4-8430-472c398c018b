ig.module('game.controllers.menu-controller')
.requires(
    'plugins.utils.entity-extended',
    'plugins.utils.transition',
    'game.entities.buttons.button-play',
    'game.entities.buttons.button-settings',
    'game.entities.buttons.button-more-games'
)
.defines(function () {
    "use strict";

    ig.MenuController = ig.global.MenuController = ig.EntityExtended.extend({
        zIndex: 1,
        name: 'menu-controller',
        bgImage: new ig.Image('media/graphics/sprites/ui/menu-bg.png'),
        titleImage: new ig.Image('media/graphics/sprites/ui/title.png'),
        positions: {
            title: {},
            playButton: {}
        },
        buttons: {},
        transition: null,
        init: function (x, y, settings) {
            this.parent(x, y, settings);
            this.ctx = ig.system.context;
            this.transition = ig.game.spawnEntity(EntityTransition, 0, 0, { color: 'black' });
            this.transition.fadeOut(1000);
            this.initPositions();
            this.initButtons();
            this.tweenIn();
            window.menuctrl = this;
            ig.currentCtrl = this;
            ig.game.sortEntitiesDeferred();
        },

        initPositions: function () {
            this.positions.title.x = 0;
            this.positions.title.y = 0;
            this.positions.title._x = ig.system.width * 0.5 - this.titleImage.width * 0.5;
            this.positions.title._y = 100;
            this.positions.title.alpha = 0;
        },

        initButtons: function () {
            this.buttons.updateAlpha = function (alpha) {
                for (var key in this.buttons) {
                    if (this.buttons[key] && typeof this.buttons[key].updateAlpha === 'function') {
                        this.buttons[key].updateAlpha(alpha);
                    }
                }
            }.bind(this);

            this.buttons.disable = function () {
                for (var key in this.buttons) {
                    if (this.buttons[key] && typeof this.buttons[key].disable === 'function') {
                        this.buttons[key].disable();
                    }
                }
            }.bind(this);

            this.buttons.enable = function () {
                for (var key in this.buttons) {
                    if (this.buttons[key] && typeof this.buttons[key].enable === 'function') {
                        this.buttons[key].enable();
                    }
                }
            }.bind(this);

            this.buttons.play = this.spawnEntity(EntityButtonPlay, -999, -999);
            this.buttons.play.pos.x = ig.system.width * 0.5 - this.buttons.play.size.x * 0.5;
            this.buttons.play.pos.y = this.positions.title._y + this.titleImage.height + 80;

            this.buttons.settings = this.spawnEntity(EntityButtonSettings, -999, -999);
            this.buttons.settings.pos.x = ig.system.width * 0.5 - this.buttons.settings.size.x * 0.5;
            this.buttons.settings.pos.y = this.buttons.play.pos.y + this.buttons.play.size.y + 20;

            this.buttons.moreGames = this.spawnEntity(EntityButtonMoreGames, -999, -999);
            this.buttons.moreGames.pos.x = ig.system.width * 0.5 - this.buttons.moreGames.size.x * 0.5;
            this.buttons.moreGames.pos.y = this.buttons.settings.pos.y + this.buttons.settings.size.y + 20;
        },

        tweenIn: function () {
            this.positions.title.x = this.positions.title._x;
            this.positions.title.y = this.positions.title._y - 100;
            this.positions.title.alpha = 0;
            var titleTween = new ig.TweenDef(this.positions.title)
                .to({ x: this.positions.title._x, y: this.positions.title._y, alpha: 1 }, 800)
                .easing(ig.Tween.Easing.Cubic.EaseOut);
            
            this.buttons.updateAlpha(0);
            this.buttons.disable();
            var alpha = { value: 0 };
            var buttonAlphaTween = new ig.TweenDef(alpha)
                .to({ value: 1 }, 800)
                .easing(ig.Tween.Easing.Cubic.EaseOut)
                .onUpdate(function () {
                    this.buttons.updateAlpha(alpha.value);
                }.bind(this))
                .onComplete(function () {
                    this.buttons.enable();
                }.bind(this));
            
            titleTween.chain(buttonAlphaTween);
            titleTween.start();
        },

        tweenHide: function () {
            var titleTween = new ig.TweenDef(this.positions.title)
                .to({ alpha: 0 }, 100)
                .easing(ig.Tween.Easing.Cubic.EaseIn)
                .start();
            
            this.buttons.updateAlpha(0);
            this.buttons.disable();
            var alpha = { value: 1 };
            var buttonAlphaTween = new ig.TweenDef(alpha)
                .to({ value: 0 }, 100)
                .easing(ig.Tween.Easing.Cubic.EaseIn)
                .onUpdate(function () {
                    this.buttons.updateAlpha(alpha.value);
                }.bind(this))
                .onComplete(function () {
                    this.buttons.disable();
                }.bind(this))
                .start();
        },

        tweenShow: function () {
            var titleTween = new ig.TweenDef(this.positions.title)
                .to({ alpha: 1 }, 100)
                .easing(ig.Tween.Easing.Cubic.EaseOut)
                .start();
            
            this.buttons.updateAlpha(0);
            this.buttons.disable();
            var alpha = { value: 0 };
            var buttonAlphaTween = new ig.TweenDef(alpha)
                .to({ value: 1 }, 100)
                .easing(ig.Tween.Easing.Cubic.EaseOut)
                .onUpdate(function () {
                    this.buttons.updateAlpha(alpha.value);
                }.bind(this))
                .onComplete(function () {
                    this.buttons.enable();
                }.bind(this))
                .start();
        },
        
        update: function () {
            this.parent();
        },
        
        draw: function () {
            this.parent();
            this.bgImage.draw(0, 0);
            this.ctx.save();
            this.ctx.globalAlpha = this.positions.title.alpha;
            this.titleImage.draw(this.positions.title.x, this.positions.title.y);
            this.ctx.restore();
        }
    });
});
