<!DOCTYPE html>
<html>

<head>
	<title>Truck Master</title>
	<link rel="icon" type="image/png" href="media/graphics/misc/favicon.ico" />
	<meta http-equiv="Content-type" content="text/html; charset=utf-8">
	<meta name="viewport" content="width=device-width,height=device-height, initial-scale=1, maximum-scale=1, user-scalable=0, shrink-to-fit=no, minimal-ui" />
	<meta name="apple-mobile-web-app-capable" content="yes" />
	<link rel="stylesheet" type="text/css" href="game.css">
	<script type="text/javascript" src="game.js"></script>

	<!-- AnalyticsCode -->

	<!-- APICode -->
</head>

<body onload="setTimeout(function(){window.scrollTo(0,1)},1);">
	<!--body-->
	<div id="ajaxbar">
		<div id="game"><canvas id="canvas"></canvas></div>

		<div id="orientate"><img src="media/graphics/orientate/portrait.png" /></div>
		<div id="play" class="play" onclick=""></div>
		<!--<img id="scrollDown" width="220" height="277"></img>-->

		<!-- SECTION GENERATED BY CODE -->
		<div id="MobileAdInGamePreroll">
			<div id="MobileAdInGamePreroll-Box">
				<div id="MobileAdInGamePreroll-Box-Header"></div>
				<a id="MobileAdInGamePreroll-Box-Close" onclick="MobileAdInGamePreroll.Close();"></a>
				<div id="MobileAdInGamePreroll-Box-Body">
					<center>
						<!-- MobileAdInGamePreroll -->
						<!-- AdTest-MobileAdInGamePreroll -->
						<img src="https://cdn-factory.marketjs.com/generic.png">
						<!-- EndOfAdTest-MobileAdInGamePreroll -->
					</center>
				</div>
				<div id='MobileAdInGamePreroll-Box-Footer'></div>
			</div>
		</div>

		<!-- SECOND AD (ROTATION) -->
		<div id="MobileAdInGamePreroll2">
			<div id="MobileAdInGamePreroll2-Box">
				<div id="MobileAdInGamePreroll2-Box-Header"></div>
				<a id="MobileAdInGamePreroll2-Box-Close" onclick="MobileAdInGamePreroll.Close();"></a>
				<div id="MobileAdInGamePreroll2-Box-Body">
					<center>
						<!-- MobileAdInGamePreroll2 -->
					</center>
				</div>
				<div id='MobileAdInGamePreroll2-Box-Footer'></div>
			</div>
		</div>

		<!-- THIRD AD (ROTATION) -->
		<div id="MobileAdInGamePreroll3">
			<div id="MobileAdInGamePreroll3-Box">
				<div id="MobileAdInGamePreroll3-Box-Header"></div>
				<a id="MobileAdInGamePreroll3-Box-Close" onclick="MobileAdInGamePreroll.Close();"></a>
				<div id="MobileAdInGamePreroll3-Box-Body">
					<center>
						<!-- MobileAdInGamePreroll3 -->
					</center>
				</div>
				<div id='MobileAdInGamePreroll3-Box-Footer'></div>
			</div>
		</div>

		<div id="MobileAdInGameHeader">
			<!-- MobileAdInGameHeader -->
		</div>
		<div id="MobileAdInGameHeader2">
			<!-- MobileAdInGameHeader2 -->
		</div>
		<div id="MobileAdInGameHeader3">
			<!-- MobileAdInGameHeader3 -->
		</div>

		<div id="MobileAdInGameFooter">
			<!-- MobileAdInGameFooter -->
		</div>
		<div id="MobileAdInGameFooter2">
			<!-- MobileAdInGameFooter2 -->
		</div>
		<div id="MobileAdInGameFooter3">
			<!-- MobileAdInGameFooter3 -->
		</div>


		<div id="MobileAdInGameEnd">
			<div id="MobileAdInGameEnd-Box">
				<div id="MobileAdInGameEnd-Box-Header"></div>
				<a id="MobileAdInGameEnd-Box-Close" onclick="MobileAdInGameEnd.Close();"></a>
				<div id="MobileAdInGameEnd-Box-Body">
					<center>
						<!-- MobileAdInGameEnd -->
					</center>
				</div>
				<div id='MobileAdInGameEnd-Box-Footer'></div>
			</div>
		</div>

		<!-- SECOND AD (ROTATION) -->
		<div id="MobileAdInGameEnd2">
			<div id="MobileAdInGameEnd2-Box">
				<div id="MobileAdInGameEnd2-Box-Header"></div>
				<a id="MobileAdInGameEnd2-Box-Close" onclick="MobileAdInGameEnd.Close();"></a>
				<div id="MobileAdInGameEnd2-Box-Body">
					<center>
						<!-- MobileAdInGameEnd2 -->
					</center>
				</div>
				<div id='MobileAdInGameEnd2-Box-Footer'></div>
			</div>
		</div>

		<!-- THIRD AD (ROTATION) -->
		<div id="MobileAdInGameEnd3">
			<div id="MobileAdInGameEnd3-Box">
				<div id="MobileAdInGameEnd3-Box-Header"></div>
				<a id="MobileAdInGameEnd3-Box-Close" onclick="MobileAdInGameEnd.Close();"></a>
				<div id="MobileAdInGameEnd3-Box-Body">
					<center>
						<!-- MobileAdInGameEnd3 -->
					</center>
				</div>
				<div id='MobileAdInGameEnd3-Box-Footer'></div>
			</div>
		</div>

		<!-- END OF SECTION GENERATED BY CODE -->

	</div>
	<!-- <div id="tempdiv"><br><br><br></div> -->
	<!-- APICode2 -->


	<!-- END OF TEST -->

</body>

</html>