ig.module('game.entities.objects.popup-gameover')
.requires(
    'plugins.utils.objects.popup-base',
    'game.entities.text',
    'game.entities.buttons.button-main-menu',
    'game.entities.buttons.button-replay'
)
.defines(function () {

    EntityPopupGameOver = EntityPopupBase.extend({
        name: 'popup-gameover',

        // --- Configuration for the Game Over Popup ---

        // Override default header text settings from EntityPopupBase
        headerTextConfig: {
            text: 'Game Over!',
            fontSize: 120,
            fontFamily: 'bebasneue-bold',
            fontColor: '#FFFFFF',
            align: 'center',
            vAlign: 'middle',
            shadowEnabled: true,
            shadowOffsetX: 4,
            shadowOffsetY: 4,
            shadowBlur: 1,
            shadowColor: '#000000',
            overflow: true
        },
        
        // Configuration for the body text (Score, Best Score)
        bodyTextConfig: {
            text: 'Score: 0\nBest: 0',
            fontSize: 84,
            fontFamily: 'bebasneue-bold',
            fontColor: '#FFFFFF',
            align: 'center',
            vAlign: 'middle',
            shadowEnabled: true,
            shadowOffsetX: 4,
            shadowOffsetY: 4,
            shadowBlur: 1,
            shadowColor: '#000000',
            overflow: true
        },

        // Offset for the body text relative to the popup's center
        // Adjust these values to position the body text appropriately below the header
        bodyTextOffset: { x: 0, y: 0 },
        headerTextOffset: { x: 0, y: 60 },
        // Store references to game score variables if needed
        currentScore: 1,
        bestScore: 2,

        displayOverlay: false,

        hasCloseButton: false,

        init: function (x, y, settings) {
            // Update scores if passed in settings
            if (settings && typeof settings.currentScore !== 'undefined') {
                this.currentScore = settings.currentScore;
            }
            if (settings && typeof settings.bestScore !== 'undefined') {
                this.bestScore = settings.bestScore;
            }
            
            this.bodyTextConfig.text = 'Score: ' + this.currentScore + '\nBest: ' + this.bestScore;

            this.parent(x, y, settings);

            // --- Spawn Body Text Entity ---
            var bodyConfig = {};
            ig.merge(bodyConfig, this.bodyTextConfig); // Start with defaults for body

            // Override with settings passed for bodyTextConfig if any
            if (settings && settings.bodyTextConfig) {
                ig.merge(bodyConfig, settings.bodyTextConfig);
            }

            bodyConfig.width = bodyConfig.width || (this.size.x * 0.8);
            bodyConfig.height = bodyConfig.height || (this.size.y * 0.3); 

            // Determine the offset for the body text
            var currentBodyTextOffset = (settings && settings.bodyTextOffset) ? settings.bodyTextOffset : this.bodyTextOffset;

            var bodyTextEntitySettings = {
                textConfig: bodyConfig,
                alpha: this.popupAlpha,
                zIndex: this.zIndex + 1
            };
            
            var initialBodyX = this.pos.x; // Temporary, will be updated by anchor
            var initialBodyY = this.pos.y; // Temporary

            this.elements.bodyText = ig.game.spawnEntity(
                EntityText, 
                initialBodyX, 
                initialBodyY, 
                bodyTextEntitySettings
            );

            if (this.elements.bodyText) {
                // Anchor the body text to the center of this popup, applying the bodyTextOffset
                this.elements.bodyText.anchorTo(
                    this,
                    {
                        targetAnchor: { x: 0, y: 0 },
                        selfAnchor: "center-middle",
                        offset: currentBodyTextOffset
                    }
                );
            }
            // --- End Body Text Initialization ---
            this.elements.buttons = {};
            this.elements.buttons.menu = this.spawnEntity(
                EntityButtonMainMenu,
                this.pos.x,
                this.pos.y
            );
            this.elements.buttons.replay = this.spawnEntity(
                EntityButtonReplay,
                this.pos.x,
                this.pos.y
            );

            this.updateScores(this.currentScore, this.bestScore);

            ig.game.sortEntitiesDeferred();
        },

        /**
         * Update scores and refresh the body text.
         * @param {number} currentScore - The player's final score.
         * @param {number} bestScore - The best score achieved.
         */
        updateScores: function (currentScore, bestScore) {
            this.currentScore = currentScore;
            this.bestScore = bestScore;
            var newBodyText = 'Score: ' + this.currentScore + '\nBest: ' + this.bestScore;
            
            if (this.elements.bodyText) {
                this.elements.bodyText.setTextContent(newBodyText);
            } else {
                // Fallback if bodyText entity isn't created yet (should not happen if init is correct)
                this.bodyTextConfig.text = newBodyText;
            }
        },

        updateElementsAlpha: function (alpha) {
            this.parent(alpha); // Call parent method to update header, close button, overlay

            if (this.elements.bodyText && typeof this.elements.bodyText.updateAlpha === 'function') {
                this.elements.bodyText.updateAlpha(alpha);
            }

            if (this.elements.buttons) {
                for (var key in this.elements.buttons) {
                    if (this.elements.buttons[key] && typeof this.elements.buttons[key].updateAlpha === 'function') {
                        this.elements.buttons[key].updateAlpha(alpha);
                    }
                }
            }
        },

        updateElementsPosition: function () {
            this.parent(); // Call parent method to update header, close button

            if (this.elements.buttons) {
                var padding = 20;
                this.elements.buttons.menu.pos.x = this.pos.x + (this.size.x * 0.5) - (this.elements.buttons.menu.size.x) - padding;
                this.elements.buttons.menu.pos.y = this.pos.y + this.size.y * 0.8;

                this.elements.buttons.replay.pos.x = this.pos.x + (this.size.x * 0.5) + padding;
                this.elements.buttons.replay.pos.y = this.elements.buttons.menu.pos.y;

                this.elements.buttons.menu.textEntity._updateAnchorPosition();
                this.elements.buttons.replay.textEntity._updateAnchorPosition();
            }

            if (this.elements.bodyText) this.elements.bodyText._updateAnchorPosition();
            if (this.elements.headerText) this.elements.headerText._updateAnchorPosition();
        },

        kill: function () {
            if (this.elements.bodyText) {
                this.elements.bodyText.kill();
            }
            if (this.elements.buttons) {
                this.elements.buttons.menu.kill();
                this.elements.buttons.replay.kill();                
            }
            // Kill other custom buttons if added
            this.parent();
        },

        update: function () {
            this.parent();
        }
    });
});
