ig.module(
    'game.entities.warning-signal'
)
.requires(
    'plugins.utils.entity-extended',
    'plugins.tweens-handler'
)
.defines(function () {

    EntityWarningSignal = ig.EntityExtended.extend({
        size: { x: 50, y: 50 },
        zIndex: 1000,

        idleSheetInfo: { sheetImage: new ig.Image('media/graphics/sprites/ui/warning.png'), frameCountX: 1, frameCountY: 1 },

        _blinkAlpha: 0,
        isBlinking: false,

        // Default blink settings; can be overridden by spawner (e.g., SpawnController).
        blinkCount: 6,      // Default number of times the signal appears (blinks on).
        blinkDuration: 1,   // Default duration of one full blink cycle (on and off) in seconds.

        direction: -1,      // Screen edge direction for positioning (0:Left, 1:Top, 2:Right, 3:Bot).
        _activeTween: null, // Stores the reference to the active blinking tween.

        init: function (x, y, settings) {
            this.parent(x, y, settings);

            this.ctx = ig.system.context;

            this.blinkCount = settings.blinkCount || this.blinkCount;
            this.blinkDuration = settings.blinkDuration || this.blinkDuration;
            this.anims.idle = new ig.Animation(this.idleSheet, 1, [0], true);
            this.currentAnim = this.anims.idle;

            var padding = 10;
            switch (settings.direction) {
                case 0: // Left
                    this.pos.x = padding;
                    this.pos.y = this.pos.y - this.size.y / 2;
                    break;
                case 1: // Top
                    this.pos.x = this.pos.x - this.size.x / 2;
                    this.pos.y = padding;
                    break;
                case 2: // Right
                    this.pos.x = ig.system.width - this.size.x - padding;
                    this.pos.y = this.pos.y - this.size.y / 2;
                    break;
                case 3: // Bot
                    this.pos.x = this.pos.x - this.size.x / 2;
                    this.pos.y = ig.system.height - this.size.y - padding;
                    break;
            }
            // console.log("WarningSignal init. blinkCount: " + this.blinkCount + ", blinkDuration: " + this.blinkDuration + ", X: " + x + ", Y: " + y);
            
            this._activeTween = null;

            if (settings.autoStart !== false) { // Auto-start blinking unless specified otherwise.
                this.resetBlinking();
            }
        },

        startBlinkingAnimation: function () {
            if (this._activeTween) { // Ensure any previous tween is removed.
                ig.game.tweens.remove(this._activeTween);
                this._activeTween = null;
            }

            this.isBlinking = true;
            this._blinkAlpha = 0; // Start fully transparent.

            this._activeTween = new ig.TweenDef(this)
                .to({ _blinkAlpha: 1 }, (this.blinkDuration / 2) * 1000) // Time to fade in (half blink duration).
                .yoyo(true) // Fade out automatically.
                .repeat(this.blinkCount * 2 - 1) // Total phases (e.g., 6 blinks = 11 phases: on,off,on,off,on,off,on,off,on,off,on).
                .onComplete(function () {
                    this.isBlinking = false;
                    this._blinkAlpha = 0; // Ensure it ends fully transparent.
                    this._activeTween = null;
                    if (typeof this.onBlinkingComplete === 'function') {
                        this.onBlinkingComplete();
                    }
                }.bind(this));
            
            this._activeTween.start();
        },

        update: function () {
            this.parent();
        },

        draw: function () {
            if (this._blinkAlpha < 0.11) {
                return;
            }
            
            this.ctx.save();
            this.ctx.globalAlpha = this._blinkAlpha;
            this.parent();
            this.ctx.restore();
        },

        onBlinkingComplete: function () {},

        /**
         * Resets and restarts the blinking animation. Can apply new settings before restarting.
         * @param {Object} [newSettings] - Optional. If provided, updates blinkCount, blinkDuration, and size.
         *		 @param {number} [newSettings.blinkCount] - Overrides the current blink count.
         *		 @param {number} [newSettings.blinkDuration] - Overrides the current blink duration.
         *		 @param {Object} [newSettings.size] - Overrides the current entity size.
         */
        resetBlinking: function (newSettings) {
            if (newSettings) {
                this.blinkCount = newSettings.blinkCount || this.blinkCount;
                this.blinkDuration = newSettings.blinkDuration || this.blinkDuration;
                if (newSettings.size) {
                    this.size = newSettings.size;
                }
            }
            this.startBlinkingAnimation();
        },
        
        kill: function () {
            if (this._activeTween) {
                ig.game.tweens.remove(this._activeTween);
                this._activeTween = null;
            }
            this.parent();
        }
    });

});
